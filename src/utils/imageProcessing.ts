/**
 * Image processing utilities for NeoVizion
 */

export interface ImageProcessingOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  normalize?: boolean;
  sharpen?: boolean;
}

/**
 * Preprocess image for optimal VLM analysis
 */
export async function preprocessImage(
  file: File,
  options: ImageProcessingOptions = {}
): Promise<Blob> {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.9,
    format = 'jpeg',
    normalize = true,
    sharpen = false,
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      try {
        const { width, height } = img;
        
        // Calculate new dimensions maintaining aspect ratio
        let newWidth = width;
        let newHeight = height;
        
        if (width > maxWidth) {
          newWidth = maxWidth;
          newHeight = (height * maxWidth) / width;
        }
        
        if (newHeight > maxHeight) {
          newHeight = maxHeight;
          newWidth = (newWidth * maxHeight) / newHeight;
        }
        
        canvas.width = newWidth;
        canvas.height = newHeight;
        
        // Apply image smoothing for better quality
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // Draw the image
        ctx.drawImage(img, 0, 0, newWidth, newHeight);
        
        // Apply image processing filters
        if (normalize || sharpen) {
          const imageData = ctx.getImageData(0, 0, newWidth, newHeight);
          const data = imageData.data;
          
          if (normalize) {
            normalizeImageData(data);
          }
          
          if (sharpen) {
            sharpenImageData(data, newWidth, newHeight);
          }
          
          ctx.putImageData(imageData, 0, 0);
        }
        
        // Convert to blob
        const mimeType = `image/${format}`;
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to process image'));
            }
          },
          mimeType,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Normalize image brightness and contrast
 */
function normalizeImageData(data: Uint8ClampedArray): void {
  let min = 255;
  let max = 0;
  
  // Find min and max values (excluding alpha channel)
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
    
    min = Math.min(min, gray);
    max = Math.max(max, gray);
  }
  
  const range = max - min;
  if (range === 0) return; // Avoid division by zero
  
  // Normalize each pixel
  for (let i = 0; i < data.length; i += 4) {
    data[i] = Math.round(((data[i] - min) / range) * 255);     // R
    data[i + 1] = Math.round(((data[i + 1] - min) / range) * 255); // G
    data[i + 2] = Math.round(((data[i + 2] - min) / range) * 255); // B
    // Alpha channel (i + 3) remains unchanged
  }
}

/**
 * Apply sharpening filter to image data
 */
function sharpenImageData(data: Uint8ClampedArray, width: number, height: number): void {
  const sharpenKernel = [
    0, -1, 0,
    -1, 5, -1,
    0, -1, 0
  ];
  
  const tempData = new Uint8ClampedArray(data);
  
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      for (let c = 0; c < 3; c++) { // RGB channels only
        let sum = 0;
        
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const pixelIndex = ((y + ky) * width + (x + kx)) * 4 + c;
            const kernelIndex = (ky + 1) * 3 + (kx + 1);
            sum += tempData[pixelIndex] * sharpenKernel[kernelIndex];
          }
        }
        
        const pixelIndex = (y * width + x) * 4 + c;
        data[pixelIndex] = Math.max(0, Math.min(255, sum));
      }
    }
  }
}

/**
 * Extract dominant colors from image
 */
export async function extractDominantColors(
  file: File,
  colorCount: number = 5
): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      try {
        // Use smaller canvas for color extraction
        const size = 100;
        canvas.width = size;
        canvas.height = size;
        
        ctx.drawImage(img, 0, 0, size, size);
        const imageData = ctx.getImageData(0, 0, size, size);
        const data = imageData.data;
        
        // Simple color quantization
        const colorMap = new Map<string, number>();
        
        for (let i = 0; i < data.length; i += 4) {
          const r = Math.floor(data[i] / 32) * 32;
          const g = Math.floor(data[i + 1] / 32) * 32;
          const b = Math.floor(data[i + 2] / 32) * 32;
          
          const color = `rgb(${r},${g},${b})`;
          colorMap.set(color, (colorMap.get(color) || 0) + 1);
        }
        
        // Sort by frequency and return top colors
        const sortedColors = Array.from(colorMap.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, colorCount)
          .map(([color]) => color);
        
        resolve(sortedColors);
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Calculate image quality metrics
 */
export async function calculateImageQuality(file: File): Promise<{
  sharpness: number;
  brightness: number;
  contrast: number;
}> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      try {
        canvas.width = img.width;
        canvas.height = img.height;
        
        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, img.width, img.height);
        const data = imageData.data;
        
        let totalBrightness = 0;
        let totalVariance = 0;
        let sharpnessSum = 0;
        const pixelCount = data.length / 4;
        
        // Calculate brightness and prepare for other metrics
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
          totalBrightness += brightness;
        }
        
        const avgBrightness = totalBrightness / pixelCount;
        
        // Calculate contrast (variance)
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
          totalVariance += Math.pow(brightness - avgBrightness, 2);
        }
        
        const contrast = Math.sqrt(totalVariance / pixelCount);
        
        // Simple sharpness calculation using gradient magnitude
        for (let y = 1; y < img.height - 1; y++) {
          for (let x = 1; x < img.width - 1; x++) {
            const idx = (y * img.width + x) * 4;
            const rightIdx = (y * img.width + (x + 1)) * 4;
            const bottomIdx = ((y + 1) * img.width + x) * 4;
            
            const gx = data[rightIdx] - data[idx];
            const gy = data[bottomIdx] - data[idx];
            const gradient = Math.sqrt(gx * gx + gy * gy);
            sharpnessSum += gradient;
          }
        }
        
        const sharpness = sharpnessSum / ((img.width - 2) * (img.height - 2));
        
        resolve({
          sharpness: Math.min(100, sharpness / 2.55), // Normalize to 0-100
          brightness: (avgBrightness / 255) * 100,    // Normalize to 0-100
          contrast: Math.min(100, contrast / 1.28),   // Normalize to 0-100
        });
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

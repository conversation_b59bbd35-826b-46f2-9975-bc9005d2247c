import { useCallback, useRef, useMemo, useEffect, useState } from 'react';

/**
 * Performance monitoring utilities
 */

// Performance metrics interface
export interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  memoryUsage?: number;
  timestamp: number;
}

// Performance monitor class
class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics = 100;

  recordMetric(metric: PerformanceMetrics) {
    this.metrics.push(metric);
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }
  }

  getAverageRenderTime(): number {
    if (this.metrics.length === 0) return 0;
    const total = this.metrics.reduce((sum, metric) => sum + metric.renderTime, 0);
    return total / this.metrics.length;
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  clear() {
    this.metrics = [];
  }
}

export const performanceMonitor = new PerformanceMonitor();

/**
 * Hook for measuring component render performance
 */
export const useRenderPerformance = (componentName: string) => {
  const renderStartTime = useRef<number>(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    performanceMonitor.recordMetric({
      renderTime,
      componentCount: 1,
      timestamp: Date.now(),
    });

    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
    }
  });
};

/**
 * Optimized debounce hook
 */
export const useOptimizedDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T => {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const callbackRef = useRef(callback);

  // Update callback ref when dependencies change
  useEffect(() => {
    callbackRef.current = callback;
  }, deps);

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args);
      }, delay);
    }) as T,
    [delay]
  );
};

/**
 * Optimized throttle hook
 */
export const useOptimizedThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T => {
  const lastCallTime = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, deps);

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      const timeSinceLastCall = now - lastCallTime.current;

      if (timeSinceLastCall >= delay) {
        lastCallTime.current = now;
        callbackRef.current(...args);
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          lastCallTime.current = Date.now();
          callbackRef.current(...args);
        }, delay - timeSinceLastCall);
      }
    }) as T,
    [delay]
  );
};

/**
 * Memoized selector hook for complex state selections
 */
export const useMemoizedSelector = <T, R>(
  selector: (state: T) => R,
  state: T,
  deps: React.DependencyList = []
): R => {
  return useMemo(() => selector(state), [state, ...deps]);
};

/**
 * Intersection Observer hook for lazy loading
 */
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const elementRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | undefined>(undefined);
  const isIntersecting = useRef(false);
  const callbackRef = useRef<((isIntersecting: boolean) => void) | undefined>(undefined);

  const setCallback = useCallback((callback: (isIntersecting: boolean) => void) => {
    callbackRef.current = callback;
  }, []);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        const intersecting = entry.isIntersecting;
        if (intersecting !== isIntersecting.current) {
          isIntersecting.current = intersecting;
          callbackRef.current?.(intersecting);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [options]);

  return { elementRef, setCallback };
};

/**
 * Virtual scrolling utilities
 */
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export const useVirtualScroll = <T>(
  items: T[],
  options: VirtualScrollOptions
) => {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  const scrollTop = useRef(0);

  const visibleRange = useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.floor(scrollTop.current / itemHeight);
    const endIndex = Math.min(
      startIndex + visibleCount + overscan,
      items.length - 1
    );

    return {
      startIndex: Math.max(0, startIndex - overscan),
      endIndex,
      visibleCount,
    };
  }, [items.length, itemHeight, containerHeight, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.startIndex * itemHeight;

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    scrollTop.current = event.currentTarget.scrollTop;
  }, []);

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    visibleRange,
  };
};

/**
 * Image lazy loading hook
 */
export const useLazyImage = (src: string, placeholder?: string) => {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const { elementRef, setCallback } = useIntersectionObserver();

  useEffect(() => {
    setCallback((isIntersecting) => {
      if (isIntersecting && !isLoaded && !isError) {
        const img = new Image();
        img.onload = () => {
          setImageSrc(src);
          setIsLoaded(true);
        };
        img.onerror = () => {
          setIsError(true);
        };
        img.src = src;
      }
    });
  }, [src, isLoaded, isError, setCallback]);

  return {
    elementRef,
    imageSrc,
    isLoaded,
    isError,
  };
};

/**
 * Memory usage monitoring
 */
export const useMemoryMonitor = () => {
  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
      };
    }
    return null;
  }, []);

  const logMemoryUsage = useCallback((label: string) => {
    const usage = getMemoryUsage();
    if (usage && process.env.NODE_ENV === 'development') {
      console.log(`Memory usage (${label}):`, {
        used: `${(usage.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(usage.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(usage.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
        percentage: `${usage.usagePercentage.toFixed(2)}%`,
      });
    }
  }, [getMemoryUsage]);

  return { getMemoryUsage, logMemoryUsage };
};

/**
 * Component update tracker for debugging
 */
export const useUpdateTracker = (name: string, props: Record<string, any>) => {
  const prevProps = useRef<Record<string, any> | undefined>(undefined);

  useEffect(() => {
    if (prevProps.current && process.env.NODE_ENV === 'development') {
      const changedProps = Object.keys(props).filter(
        key => props[key] !== prevProps.current![key]
      );
      
      if (changedProps.length > 0) {
        console.log(`${name} updated due to:`, changedProps);
      }
    }
    prevProps.current = props;
  });
};

/**
 * Batch state updates to reduce re-renders
 */
export const useBatchedUpdates = () => {
  const updates = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const batchUpdate = useCallback((updateFn: () => void) => {
    updates.current.push(updateFn);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      const currentUpdates = updates.current;
      updates.current = [];
      
      // Execute all batched updates
      currentUpdates.forEach(update => update());
    }, 0);
  }, []);

  return { batchUpdate };
};

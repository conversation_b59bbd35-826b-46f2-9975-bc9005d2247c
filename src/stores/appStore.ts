import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { 
  AppState, 
  UploadedImage, 
  DetectedProduct, 
  ProductInfo, 
  FilterOptions 
} from '../types';

interface AppStore extends AppState {
  // Actions
  setCurrentImage: (image: UploadedImage | null) => void;
  setSelectedProduct: (product: DetectedProduct | null) => void;
  setProductInfo: (productId: string, info: ProductInfo) => void;
  setFilters: (filters: FilterOptions) => void;
  setProcessing: (isProcessing: boolean) => void;
  setError: (error: string | null) => void;
  updateImageProducts: (imageId: string, products: DetectedProduct[]) => void;
  
  // Computed values
  getFilteredProducts: () => DetectedProduct[];
  getProductInfo: (productId: string) => ProductInfo | undefined;
  
  // Reset functions
  reset: () => void;
  clearError: () => void;
}

const initialFilters: FilterOptions = {
  dietary: {
    vegan: false,
    vegetarian: false,
    glutenFree: false,
    halal: false,
    kosher: false,
  },
  exclude: {
    pork: false,
    beef: false,
    alcohol: false,
    nitrates: false,
    allergens: [],
  },
  categories: [],
};

const initialState: AppState = {
  currentImage: null,
  selectedProduct: null,
  productInfo: {},
  filters: initialFilters,
  isProcessing: false,
  error: null,
};

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setCurrentImage: (image) => 
        set({ currentImage: image, selectedProduct: null }, false, 'setCurrentImage'),

      setSelectedProduct: (product) => 
        set({ selectedProduct: product }, false, 'setSelectedProduct'),

      setProductInfo: (productId, info) =>
        set(
          (state) => ({
            productInfo: { ...state.productInfo, [productId]: info },
          }),
          false,
          'setProductInfo'
        ),

      setFilters: (filters) => 
        set({ filters }, false, 'setFilters'),

      setProcessing: (isProcessing) => 
        set({ isProcessing }, false, 'setProcessing'),

      setError: (error) => 
        set({ error }, false, 'setError'),

      updateImageProducts: (imageId, products) =>
        set(
          (state) => {
            if (state.currentImage?.id === imageId) {
              return {
                currentImage: {
                  ...state.currentImage,
                  detectedProducts: products,
                  processed: true,
                },
              };
            }
            return state;
          },
          false,
          'updateImageProducts'
        ),

      getFilteredProducts: () => {
        const { currentImage, filters, productInfo } = get();
        if (!currentImage?.detectedProducts) return [];

        return currentImage.detectedProducts.filter((product) => {
          const info = productInfo[product.id];
          if (!info) return true; // Show products without info by default

          // Check dietary filters
          if (filters.dietary.vegan && !info.dietaryTags?.includes('vegan')) {
            return false;
          }
          if (filters.dietary.vegetarian && !info.dietaryTags?.includes('vegetarian')) {
            return false;
          }
          if (filters.dietary.glutenFree && !info.dietaryTags?.includes('gluten-free')) {
            return false;
          }
          if (filters.dietary.halal && !info.dietaryTags?.includes('halal')) {
            return false;
          }
          if (filters.dietary.kosher && !info.dietaryTags?.includes('kosher')) {
            return false;
          }

          // Check exclusion filters
          if (filters.exclude.pork && info.dietaryTags?.includes('contains-pork')) {
            return false;
          }
          if (filters.exclude.beef && info.dietaryTags?.includes('contains-beef')) {
            return false;
          }
          if (filters.exclude.alcohol && info.dietaryTags?.includes('contains-alcohol')) {
            return false;
          }
          if (filters.exclude.nitrates && info.dietaryTags?.includes('contains-nitrates')) {
            return false;
          }

          // Check allergen exclusions
          if (filters.exclude.allergens.length > 0 && info.allergens) {
            const hasExcludedAllergen = filters.exclude.allergens.some(allergen =>
              info.allergens?.some(productAllergen => 
                productAllergen.toLowerCase().includes(allergen.toLowerCase())
              )
            );
            if (hasExcludedAllergen) return false;
          }

          // Check category filters
          if (filters.categories.length > 0 && info.category) {
            return filters.categories.includes(info.category);
          }

          return true;
        });
      },

      getProductInfo: (productId) => {
        return get().productInfo[productId];
      },

      reset: () => 
        set(initialState, false, 'reset'),

      clearError: () => 
        set({ error: null }, false, 'clearError'),
    }),
    {
      name: 'neovizion-store',
    }
  )
);

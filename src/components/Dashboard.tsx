import React, { useState, useEffect } from 'react';
import { Settings, Download, BarChart3 } from 'lucide-react';
import { useAppStore } from '../stores/appStore';
import { useImageUpload } from '../hooks/useImageUpload';
import { useVLM } from '../hooks/useVLM';
import { useProductInfo } from '../hooks/useProductInfo';
import { useFilters } from '../hooks/useFilters';
import { Button, ErrorBoundary, LoadingOverlay } from './ui';
import UploadArea from './features/UploadArea';
import ImageViewer from './features/ImageViewer';
import ProductPanel from './features/ProductPanel';
import FilterPanel from './features/FilterPanel';
import SearchBar from './features/SearchBar';
import VLMConfig from './features/VLMConfig';
import { cn } from '../utils';

const Dashboard: React.FC = () => {
  const {
    currentImage,
    selectedProduct,
    setSelectedProduct,
    isProcessing,
    error,
    clearError,
    getFilteredProducts,
    getProductInfo,
  } = useAppStore();

  const { uploadImage } = useImageUpload();
  const { processCurrentImage, isProcessing: vlmProcessing, getProcessingStats } = useVLM();
  const { fetchMultipleProductInfo } = useProductInfo();
  const { filters, setFilters } = useFilters();

  const [showVLMConfig, setShowVLMConfig] = useState(false);
  const [showProductPanel, setShowProductPanel] = useState(false);
  const [sidebarCollapsed] = useState(false);

  const filteredProducts = getFilteredProducts();
  const processingStats = getProcessingStats();

  // Auto-process image after upload
  useEffect(() => {
    if (currentImage && !currentImage.processed && !isProcessing && !vlmProcessing) {
      processCurrentImage()
        .then((detectedProducts) => {
          // Fetch product info for all detected products
          if (detectedProducts.length > 0) {
            fetchMultipleProductInfo(detectedProducts);
          }
        })
        .catch((error) => {
          console.error('Failed to process image:', error);
        });
    }
  }, [currentImage, isProcessing, vlmProcessing, processCurrentImage, fetchMultipleProductInfo]);

  // Auto-open product panel when product is selected
  useEffect(() => {
    setShowProductPanel(!!selectedProduct);
  }, [selectedProduct]);

  const handleImageUpload = async (file: File) => {
    try {
      clearError();
      await uploadImage(file);
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  const handleProductSelect = (product: any) => {
    setSelectedProduct(product);
  };

  const handleCloseProductPanel = () => {
    setSelectedProduct(null);
    setShowProductPanel(false);
  };

  const handleDownloadResults = () => {
    if (!currentImage?.detectedProducts) return;
    
    const data = {
      image: {
        name: currentImage.file.name,
        uploadedAt: currentImage.uploadedAt,
        dimensions: {
          width: currentImage.width,
          height: currentImage.height,
        },
      },
      detectedProducts: currentImage.detectedProducts.length,
      filteredProducts: filteredProducts.length,
      products: filteredProducts.map(product => ({
        id: product.id,
        name: product.name,
        brand: product.brand,
        confidence: product.confidence,
        category: product.category,
        boundingBox: product.boundingBox,
        productInfo: getProductInfo(product.id),
      })),
      filters: filters,
      processingStats,
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `neovizion-analysis-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-secondary-50">
        {/* Header */}
        <div className="bg-white shadow-soft border-b border-secondary-200 sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Search Bar */}
              <div className="flex-1 max-w-lg">
                <SearchBar placeholder="Search products in OpenFoodFacts..." />
              </div>

              {/* Actions */}
              <div className="flex items-center gap-3">
                {processingStats && (
                  <div className="hidden md:flex items-center gap-4 text-sm text-secondary-600">
                    <span>{processingStats.totalProducts} products detected</span>
                    <span>{Math.round(processingStats.averageConfidence * 100)}% avg confidence</span>
                  </div>
                )}
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownloadResults}
                  disabled={!currentImage?.detectedProducts?.length}
                  icon={<Download className="h-4 w-4" />}
                >
                  Export
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowVLMConfig(true)}
                  icon={<Settings className="h-4 w-4" />}
                >
                  Settings
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
            {/* Left Sidebar - Filters */}
            <div className={cn(
              'lg:col-span-1 space-y-4',
              sidebarCollapsed && 'hidden lg:block'
            )}>
              <FilterPanel
                filters={filters}
                onFiltersChange={setFilters}
                productCount={currentImage?.detectedProducts?.length || 0}
                filteredCount={filteredProducts.length}
              />
              
              {/* Stats Card */}
              {processingStats && (
                <div className="bg-white rounded-xl p-4 shadow-soft">
                  <h3 className="font-semibold text-secondary-900 mb-3 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Analysis Stats
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-secondary-600">High Confidence:</span>
                      <span className="font-medium">{processingStats.highConfidenceCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Medium Confidence:</span>
                      <span className="font-medium">{processingStats.mediumConfidenceCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Low Confidence:</span>
                      <span className="font-medium">{processingStats.lowConfidenceCount}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Main Content Area */}
            <div className="lg:col-span-3 space-y-4">
              {/* Upload Area or Image Viewer */}
              <div className="h-full">
                {!currentImage ? (
                  <div className="h-full flex items-center justify-center">
                    <div className="w-full max-w-2xl">
                      <UploadArea
                        onImageUpload={handleImageUpload}
                        isProcessing={isProcessing || vlmProcessing}
                      />
                    </div>
                  </div>
                ) : (
                  <LoadingOverlay
                    isLoading={isProcessing || vlmProcessing}
                    loadingText={vlmProcessing ? "Analyzing products..." : "Processing image..."}
                    className="h-full"
                  >
                    <ImageViewer
                      image={currentImage}
                      onProductSelect={handleProductSelect}
                      selectedProduct={selectedProduct || undefined}
                      filteredProducts={filteredProducts}
                    />
                  </LoadingOverlay>
                )}
              </div>

              {/* Error Display */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-red-800">Error</h4>
                      <p className="text-sm text-red-700 mt-1">{error}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearError}
                      className="text-red-600 hover:text-red-800"
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Product Panel Modal */}
        {showProductPanel && selectedProduct && (
          <div className="fixed inset-y-0 right-0 z-50 w-full max-w-md bg-white shadow-strong border-l border-secondary-200 transform transition-transform duration-300">
            <ProductPanel
              product={selectedProduct}
              productInfo={getProductInfo(selectedProduct.id)}
              onClose={handleCloseProductPanel}
            />
          </div>
        )}

        {/* VLM Configuration Modal */}
        <VLMConfig
          isOpen={showVLMConfig}
          onClose={() => setShowVLMConfig(false)}
        />
      </div>
    </ErrorBoundary>
  );
};

export default Dashboard;

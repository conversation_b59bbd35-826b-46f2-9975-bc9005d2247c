import React from 'react';
import { Eye, Settings, Info } from 'lucide-react';
import { cn } from '../utils';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ children, className }) => {
  return (
    <div className={cn('min-h-screen bg-secondary-50', className)}>
      {/* Header */}
      <header className="bg-white shadow-soft border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-primary-600 rounded-lg">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-secondary-900">
                  NeoVizion
                </h1>
                <p className="text-xs text-secondary-600">
                  Retail Shelf Analysis
                </p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center gap-6">
              <a
                href="#"
                className="text-secondary-600 hover:text-secondary-900 transition-colors"
              >
                Dashboard
              </a>
              <a
                href="#"
                className="text-secondary-600 hover:text-secondary-900 transition-colors"
              >
                History
              </a>
              <a
                href="#"
                className="text-secondary-600 hover:text-secondary-900 transition-colors"
              >
                Analytics
              </a>
            </nav>

            {/* Actions */}
            <div className="flex items-center gap-3">
              <button className="p-2 text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 rounded-lg transition-colors">
                <Settings className="h-5 w-5" />
              </button>
              <button className="p-2 text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 rounded-lg transition-colors">
                <Info className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-secondary-200 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-secondary-600">
              © 2025 NeoVizion. Built with modern web technologies.
            </div>
            <div className="flex items-center gap-6 text-sm text-secondary-600">
              <a href="#" className="hover:text-secondary-900 transition-colors">
                Privacy
              </a>
              <a href="#" className="hover:text-secondary-900 transition-colors">
                Terms
              </a>
              <a href="#" className="hover:text-secondary-900 transition-colors">
                Support
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;

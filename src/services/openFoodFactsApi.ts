import type { 
  OpenFoodFactsProduct, 
  ProductInfo, 
  DietaryTag,
  APIError 
} from '../types';
import { extractAllergens } from '../utils';

const BASE_URL = 'https://world.openfoodfacts.org/api/v0';
const SEARCH_URL = 'https://world.openfoodfacts.org/cgi/search.pl';

// Rate limiting
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests = 100; // OpenFoodFacts allows 100 requests per minute
  private readonly timeWindow = 60 * 1000; // 1 minute

  canMakeRequest(): boolean {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    return this.requests.length < this.maxRequests;
  }

  recordRequest(): void {
    this.requests.push(Date.now());
  }

  getWaitTime(): number {
    if (this.canMakeRequest()) return 0;
    const oldestRequest = Math.min(...this.requests);
    return this.timeWindow - (Date.now() - oldestRequest);
  }
}

// Cache implementation
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly ttl = 30 * 60 * 1000; // 30 minutes

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  set<T>(key: string, data: T): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

class OpenFoodFactsApi {
  private rateLimiter = new RateLimiter();
  private cache = new ApiCache();

  private async makeRequest<T>(url: string): Promise<T> {
    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getWaitTime();
      throw new APIError(
        `Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} seconds.`,
        429
      );
    }

    // Check cache
    const cacheKey = url;
    const cached = this.cache.get<T>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      this.rateLimiter.recordRequest();
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'NeoVizion/1.0 (https://neovizion.app)',
        },
      });

      if (!response.ok) {
        throw new APIError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status
        );
      }

      const data = await response.json();
      
      // Cache successful response
      this.cache.set(cacheKey, data);
      
      return data;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0
      );
    }
  }

  async getProductByBarcode(barcode: string): Promise<ProductInfo | null> {
    try {
      const url = `${BASE_URL}/product/${barcode}.json`;
      const response = await this.makeRequest<OpenFoodFactsProduct>(url);
      
      if (response.status === 0 || !response.product) {
        return null;
      }

      return this.transformProduct(response);
    } catch (error) {
      console.error('Error fetching product by barcode:', error);
      return null;
    }
  }

  async searchProducts(query: string, limit: number = 20): Promise<ProductInfo[]> {
    try {
      const params = new URLSearchParams({
        search_terms: query,
        search_simple: '1',
        action: 'process',
        json: '1',
        page_size: limit.toString(),
        fields: 'code,product_name,brands,ingredients_text,nutriments,allergens,labels,categories,image_url,packaging,serving_size',
      });

      const url = `${SEARCH_URL}?${params}`;
      const response = await this.makeRequest<{ products: OpenFoodFactsProduct[] }>(url);
      
      if (!response.products) {
        return [];
      }

      return response.products
        .filter(product => product.product && product.product.product_name)
        .map(product => this.transformProduct(product))
        .filter((product): product is ProductInfo => product !== null);
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  }

  async searchByBrandAndName(brand: string, name: string): Promise<ProductInfo[]> {
    const query = `${brand} ${name}`.trim();
    return this.searchProducts(query, 10);
  }

  private transformProduct(response: OpenFoodFactsProduct): ProductInfo | null {
    const product = response.product;
    if (!product) return null;

    // Extract dietary tags
    const dietaryTags: DietaryTag[] = [];
    const labels = product.labels?.toLowerCase() || '';
    const categories = product.categories?.toLowerCase() || '';
    const ingredients = product.ingredients_text?.toLowerCase() || '';

    // Check for dietary labels
    if (labels.includes('vegan') || labels.includes('végétalien')) {
      dietaryTags.push('vegan');
    }
    if (labels.includes('vegetarian') || labels.includes('végétarien')) {
      dietaryTags.push('vegetarian');
    }
    if (labels.includes('gluten-free') || labels.includes('sans gluten')) {
      dietaryTags.push('gluten-free');
    }
    if (labels.includes('halal')) {
      dietaryTags.push('halal');
    }
    if (labels.includes('kosher') || labels.includes('casher')) {
      dietaryTags.push('kosher');
    }
    if (labels.includes('organic') || labels.includes('bio')) {
      dietaryTags.push('organic');
    }
    if (labels.includes('non-gmo') || labels.includes('sans ogm')) {
      dietaryTags.push('non-gmo');
    }

    // Check for meat content
    if (ingredients.includes('pork') || ingredients.includes('porc') || 
        ingredients.includes('ham') || ingredients.includes('jambon') ||
        ingredients.includes('bacon') || ingredients.includes('lard')) {
      dietaryTags.push('contains-pork');
    }
    if (ingredients.includes('beef') || ingredients.includes('bœuf') ||
        ingredients.includes('veal') || ingredients.includes('veau')) {
      dietaryTags.push('contains-beef');
    }
    if (ingredients.includes('alcohol') || ingredients.includes('alcool') ||
        ingredients.includes('wine') || ingredients.includes('vin') ||
        ingredients.includes('beer') || ingredients.includes('bière')) {
      dietaryTags.push('contains-alcohol');
    }
    if (ingredients.includes('nitrate') || ingredients.includes('nitrite') ||
        ingredients.includes('sodium nitrite') || ingredients.includes('potassium nitrate')) {
      dietaryTags.push('contains-nitrates');
    }

    // Extract allergens
    const allergens = product.allergens 
      ? product.allergens.split(',').map(a => a.trim())
      : extractAllergens(ingredients);

    // Extract nutrition facts
    const nutriments = product.nutriments;
    const nutritionFacts = nutriments ? {
      energy: nutriments.energy_100g,
      fat: nutriments.fat_100g,
      saturatedFat: nutriments['saturated-fat_100g'],
      carbohydrates: nutriments.carbohydrates_100g,
      sugars: nutriments.sugars_100g,
      fiber: nutriments.fiber_100g,
      proteins: nutriments.proteins_100g,
      salt: nutriments.salt_100g,
      sodium: nutriments.sodium_100g,
    } : undefined;

    return {
      id: response.code,
      name: product.product_name || '',
      brand: product.brands,
      barcode: response.code,
      ingredients: product.ingredients_text 
        ? product.ingredients_text.split(',').map(i => i.trim())
        : undefined,
      nutritionFacts,
      allergens,
      dietaryTags,
      imageUrl: product.image_url,
      category: product.categories?.split(',')[0]?.trim(),
      packaging: product.packaging,
      servingSize: product.serving_size,
    };
  }

  // Utility methods
  getCacheStats() {
    return {
      size: this.cache.size(),
      canMakeRequest: this.rateLimiter.canMakeRequest(),
      waitTime: this.rateLimiter.getWaitTime(),
    };
  }

  clearCache() {
    this.cache.clear();
  }
}

// Export singleton instance
export const openFoodFactsApi = new OpenFoodFactsApi();
export default openFoodFactsApi;

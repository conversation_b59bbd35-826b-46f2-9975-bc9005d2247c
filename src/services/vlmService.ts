import { generateId, sleep } from '../utils';
import type { DetectedProduct, VLMResponse, BoundingBox } from '../types';

// Mock product database for realistic testing
const MOCK_PRODUCTS = [
  {
    names: ['Coca Cola', 'Coke', 'Coca-Cola Classic'],
    brands: ['Coca-Cola', 'Coke'],
    categories: ['Beverages', 'Soft Drinks'],
    barcodes: ['049000028911', '049000042566'],
  },
  {
    names: ['Pepsi', 'Pepsi Cola'],
    brands: ['Pepsi', 'PepsiCo'],
    categories: ['Beverages', 'Soft Drinks'],
    barcodes: ['012000161155', '012000161162'],
  },
  {
    names: ['Bread', 'White Bread', 'Sandwich Bread'],
    brands: ['Wonder', 'Pepperidge Farm', 'Sara Lee'],
    categories: ['Bakery', 'Bread'],
    barcodes: ['072250017503', '014100085355'],
  },
  {
    names: ['Milk', 'Whole Milk', '2% Milk'],
    brands: ['Horizon', 'Organic Valley', 'Great Value'],
    categories: ['Dairy', 'Milk'],
    barcodes: ['042272005208', '092825106227'],
  },
  {
    names: ['Yogurt', 'Greek Yogurt', 'Vanilla Yogurt'],
    brands: ['Chobani', 'Dannon', 'Yoplait'],
    categories: ['Dairy', 'Yogurt'],
    barcodes: ['894700010045', '036632026736'],
  },
  {
    names: ['Cereal', 'Corn Flakes', 'Breakfast Cereal'],
    brands: ['Kelloggs', 'General Mills', 'Post'],
    categories: ['Breakfast', 'Cereal'],
    barcodes: ['038000045301', '016000275447'],
  },
  {
    names: ['Chocolate', 'Milk Chocolate', 'Dark Chocolate'],
    brands: ['Hersheys', 'Cadbury', 'Lindt'],
    categories: ['Confectionery', 'Chocolate'],
    barcodes: ['034000002207', '034000240104'],
  },
  {
    names: ['Pasta', 'Spaghetti', 'Penne'],
    brands: ['Barilla', 'De Cecco', 'Ronzoni'],
    categories: ['Pantry', 'Pasta'],
    barcodes: ['076808501056', '041196913637'],
  },
  {
    names: ['Rice', 'White Rice', 'Jasmine Rice'],
    brands: ['Uncle Bens', 'Mahatma', 'Lundberg'],
    categories: ['Pantry', 'Rice'],
    barcodes: ['054800220304', '041129004227'],
  },
  {
    names: ['Cheese', 'Cheddar Cheese', 'American Cheese'],
    brands: ['Kraft', 'Sargento', 'Tillamook'],
    categories: ['Dairy', 'Cheese'],
    barcodes: ['021000615919', '046100013007'],
  },
];

// VLM Service Interface
export interface VLMServiceConfig {
  apiKey?: string;
  endpoint?: string;
  model?: string;
  confidence_threshold?: number;
  max_detections?: number;
}

export interface VLMService {
  detectProducts(imageFile: File, config?: VLMServiceConfig): Promise<VLMResponse>;
  isAvailable(): boolean;
  getModelInfo(): { name: string; version: string; capabilities: string[] };
}

// Mock VLM Implementation
class MockVLMService implements VLMService {
  private config: VLMServiceConfig;

  constructor(config: VLMServiceConfig = {}) {
    this.config = {
      confidence_threshold: 0.7,
      max_detections: 20,
      ...config,
    };
  }

  async detectProducts(imageFile: File): Promise<VLMResponse> {
    const startTime = Date.now();
    
    // Simulate processing time
    await sleep(2000 + Math.random() * 3000);

    // Get image dimensions for realistic bounding boxes
    const dimensions = await this.getImageDimensions(imageFile);
    
    // Generate mock detections
    const numProducts = Math.floor(Math.random() * 8) + 3; // 3-10 products
    const detectedProducts: DetectedProduct[] = [];

    for (let i = 0; i < numProducts; i++) {
      const mockProduct = MOCK_PRODUCTS[Math.floor(Math.random() * MOCK_PRODUCTS.length)];
      const name = mockProduct.names[Math.floor(Math.random() * mockProduct.names.length)];
      const brand = mockProduct.brands[Math.floor(Math.random() * mockProduct.brands.length)];
      const barcode = mockProduct.barcodes[Math.floor(Math.random() * mockProduct.barcodes.length)];
      
      // Generate realistic bounding box
      const boundingBox = this.generateRealisticBoundingBox(dimensions, i, numProducts);
      
      detectedProducts.push({
        id: generateId(),
        name,
        brand,
        boundingBox,
        confidence: Math.random() * 0.3 + 0.7, // 0.7 - 1.0
        category: mockProduct.categories[0],
        barcode,
        extractedText: [name, brand].filter(Boolean),
      });
    }

    const processingTime = Date.now() - startTime;
    const avgConfidence = detectedProducts.reduce((sum, p) => sum + p.confidence, 0) / detectedProducts.length;

    return {
      detectedProducts,
      processingTime,
      confidence: avgConfidence,
    };
  }

  isAvailable(): boolean {
    return true; // Mock is always available
  }

  getModelInfo() {
    return {
      name: 'MockVLM',
      version: '1.0.0',
      capabilities: ['object_detection', 'text_extraction', 'product_identification'],
    };
  }

  private async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.width, height: img.height });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      
      img.src = url;
    });
  }

  private generateRealisticBoundingBox(
    dimensions: { width: number; height: number },
    index: number,
    total: number
  ): BoundingBox {
    const { width, height } = dimensions;
    
    // Create a grid-like layout for more realistic positioning
    const cols = Math.ceil(Math.sqrt(total));
    const rows = Math.ceil(total / cols);
    
    const col = index % cols;
    const row = Math.floor(index / cols);
    
    // Add some randomness to avoid perfect grid
    const cellWidth = width / cols;
    const cellHeight = height / rows;
    
    const baseX = col * cellWidth + Math.random() * cellWidth * 0.3;
    const baseY = row * cellHeight + Math.random() * cellHeight * 0.3;
    
    // Product size variations
    const minSize = Math.min(cellWidth, cellHeight) * 0.3;
    const maxSize = Math.min(cellWidth, cellHeight) * 0.8;
    
    const boxWidth = minSize + Math.random() * (maxSize - minSize);
    const boxHeight = minSize + Math.random() * (maxSize - minSize);
    
    // Ensure bounding box stays within image bounds
    const x = Math.max(0, Math.min(baseX, width - boxWidth));
    const y = Math.max(0, Math.min(baseY, height - boxHeight));
    
    return {
      x,
      y,
      width: boxWidth,
      height: boxHeight,
      confidence: Math.random() * 0.2 + 0.8, // 0.8 - 1.0 for bounding box confidence
    };
  }
}

// OpenAI GPT-4 Vision Implementation (placeholder)
class OpenAIVLMService implements VLMService {
  private config: VLMServiceConfig;

  constructor(config: VLMServiceConfig) {
    this.config = config;
  }

  async detectProducts(imageFile: File): Promise<VLMResponse> {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    // Convert image to base64
    const base64Image = await this.fileToBase64(imageFile);
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model || 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analyze this retail shelf image and identify all visible products. For each product, provide:
1. Product name
2. Brand name (if visible)
3. Bounding box coordinates (x, y, width, height) as percentages of image dimensions
4. Confidence score (0-1)
5. Any visible text on packaging
6. Product category

Return the results as a JSON array of products.`,
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`,
                },
              },
            ],
          },
        ],
        max_tokens: 4000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Parse the response and convert to our format
    // This would need proper implementation based on OpenAI's actual response format
    return this.parseOpenAIResponse(data);
  }

  isAvailable(): boolean {
    return !!this.config.apiKey;
  }

  getModelInfo() {
    return {
      name: 'GPT-4 Vision',
      version: 'preview',
      capabilities: ['object_detection', 'text_extraction', 'product_identification', 'scene_understanding'],
    };
  }

  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = (reader.result as string).split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private parseOpenAIResponse(data: any): VLMResponse {
    // This would need proper implementation based on OpenAI's response format
    // For now, return empty response
    return {
      detectedProducts: [],
      processingTime: 0,
      confidence: 0,
    };
  }
}

// VLM Service Factory
export class VLMServiceFactory {
  static createService(type: 'mock' | 'openai' = 'mock', config: VLMServiceConfig = {}): VLMService {
    switch (type) {
      case 'openai':
        return new OpenAIVLMService(config);
      case 'mock':
      default:
        return new MockVLMService(config);
    }
  }

  static getAvailableServices(): Array<{ id: string; name: string; description: string }> {
    return [
      {
        id: 'mock',
        name: 'Mock VLM',
        description: 'Mock implementation for testing and development',
      },
      {
        id: 'openai',
        name: 'OpenAI GPT-4 Vision',
        description: 'OpenAI GPT-4 with vision capabilities (requires API key)',
      },
    ];
  }
}

// Export default service instance
export const vlmService = VLMServiceFactory.createService('mock');
export default vlmService;

import { useState, useCallback, useRef, useEffect } from 'react';

export interface UseImageViewerOptions {
  minZoom?: number;
  maxZoom?: number;
  zoomStep?: number;
  enablePan?: boolean;
  enableZoom?: boolean;
}

export interface ImageViewerState {
  zoom: number;
  pan: { x: number; y: number };
  isDragging: boolean;
  imageSize: { width: number; height: number };
  containerSize: { width: number; height: number };
}

export const useImageViewer = (options: UseImageViewerOptions = {}) => {
  const {
    minZoom = 0.1,
    maxZoom = 5,
    zoomStep = 1.5,
    enablePan = true,
    enableZoom = true,
  } = options;

  const [state, setState] = useState<ImageViewerState>({
    zoom: 1,
    pan: { x: 0, y: 0 },
    isDragging: false,
    imageSize: { width: 0, height: 0 },
    containerSize: { width: 0, height: 0 },
  });

  const dragStartRef = useRef({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // Update container size
  const updateContainerSize = useCallback(() => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setState(prev => ({
        ...prev,
        containerSize: { width: rect.width, height: rect.height },
      }));
    }
  }, []);

  // Handle image load
  const handleImageLoad = useCallback(() => {
    if (imageRef.current && containerRef.current) {
      const img = imageRef.current;
      const container = containerRef.current;
      
      setState(prev => ({
        ...prev,
        imageSize: { width: img.naturalWidth, height: img.naturalHeight },
      }));
      
      // Calculate initial zoom to fit image
      const containerRect = container.getBoundingClientRect();
      const scaleX = (containerRect.width - 40) / img.naturalWidth;
      const scaleY = (containerRect.height - 40) / img.naturalHeight;
      const initialZoom = Math.min(scaleX, scaleY, 1);
      
      setState(prev => ({
        ...prev,
        zoom: initialZoom,
        pan: { x: 0, y: 0 },
      }));
    }
  }, []);

  // Zoom functions
  const zoomIn = useCallback(() => {
    if (!enableZoom) return;
    setState(prev => ({
      ...prev,
      zoom: Math.min(prev.zoom * zoomStep, maxZoom),
    }));
  }, [enableZoom, zoomStep, maxZoom]);

  const zoomOut = useCallback(() => {
    if (!enableZoom) return;
    setState(prev => ({
      ...prev,
      zoom: Math.max(prev.zoom / zoomStep, minZoom),
    }));
  }, [enableZoom, zoomStep, minZoom]);

  const setZoom = useCallback((zoom: number) => {
    if (!enableZoom) return;
    setState(prev => ({
      ...prev,
      zoom: Math.max(minZoom, Math.min(maxZoom, zoom)),
    }));
  }, [enableZoom, minZoom, maxZoom]);

  // Pan functions
  const setPan = useCallback((pan: { x: number; y: number }) => {
    if (!enablePan) return;
    setState(prev => ({ ...prev, pan }));
  }, [enablePan]);

  // Reset view
  const resetView = useCallback(() => {
    if (imageRef.current && containerRef.current) {
      const img = imageRef.current;
      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      const scaleX = (containerRect.width - 40) / img.naturalWidth;
      const scaleY = (containerRect.height - 40) / img.naturalHeight;
      const initialZoom = Math.min(scaleX, scaleY, 1);
      
      setState(prev => ({
        ...prev,
        zoom: initialZoom,
        pan: { x: 0, y: 0 },
      }));
    }
  }, []);

  // Fit to container
  const fitToContainer = useCallback(() => {
    if (imageRef.current && containerRef.current) {
      const img = imageRef.current;
      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      const scaleX = containerRect.width / img.naturalWidth;
      const scaleY = containerRect.height / img.naturalHeight;
      const fitZoom = Math.min(scaleX, scaleY);
      
      setState(prev => ({
        ...prev,
        zoom: Math.max(minZoom, Math.min(maxZoom, fitZoom)),
        pan: { x: 0, y: 0 },
      }));
    }
  }, [minZoom, maxZoom]);

  // Mouse event handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!enablePan || e.button !== 0) return;
    
    setState(prev => ({ ...prev, isDragging: true }));
    dragStartRef.current = { 
      x: e.clientX - state.pan.x, 
      y: e.clientY - state.pan.y 
    };
  }, [enablePan, state.pan]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!state.isDragging || !enablePan) return;
    
    setState(prev => ({
      ...prev,
      pan: {
        x: e.clientX - dragStartRef.current.x,
        y: e.clientY - dragStartRef.current.y,
      },
    }));
  }, [state.isDragging, enablePan]);

  const handleMouseUp = useCallback(() => {
    setState(prev => ({ ...prev, isDragging: false }));
  }, []);

  // Wheel zoom
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!enableZoom) return;
    
    e.preventDefault();
    const delta = e.deltaY > 0 ? 1 / zoomStep : zoomStep;
    setState(prev => ({
      ...prev,
      zoom: Math.max(minZoom, Math.min(maxZoom, prev.zoom * delta)),
    }));
  }, [enableZoom, zoomStep, minZoom, maxZoom]);

  // Convert screen coordinates to image coordinates
  const screenToImageCoords = useCallback((screenX: number, screenY: number) => {
    if (!containerRef.current) return { x: 0, y: 0 };
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = (screenX - rect.left - state.pan.x) / state.zoom;
    const y = (screenY - rect.top - state.pan.y) / state.zoom;
    
    return { x, y };
  }, [state.pan, state.zoom]);

  // Convert image coordinates to screen coordinates
  const imageToScreenCoords = useCallback((imageX: number, imageY: number) => {
    if (!containerRef.current) return { x: 0, y: 0 };
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = imageX * state.zoom + state.pan.x + rect.left;
    const y = imageY * state.zoom + state.pan.y + rect.top;
    
    return { x, y };
  }, [state.pan, state.zoom]);

  // Setup resize observer
  useEffect(() => {
    updateContainerSize();
    
    const resizeObserver = new ResizeObserver(updateContainerSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, [updateContainerSize]);

  return {
    // State
    ...state,
    
    // Refs
    containerRef,
    imageRef,
    
    // Actions
    zoomIn,
    zoomOut,
    setZoom,
    setPan,
    resetView,
    fitToContainer,
    
    // Event handlers
    handleImageLoad,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleWheel,
    
    // Coordinate conversion
    screenToImageCoords,
    imageToScreenCoords,
  };
};

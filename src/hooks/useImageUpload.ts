import { useCallback } from 'react';
import { useAppStore } from '../stores/appStore';
import { 
  generateId, 
  createImagePreview, 
  getImageDimensions, 
  resizeImage 
} from '../utils';
import type { UploadedImage } from '../types';

export const useImageUpload = () => {
  const { 
    setCurrentImage, 
    setProcessing, 
    setError,
    clearError 
  } = useAppStore();

  const uploadImage = useCallback(async (file: File): Promise<void> => {
    try {
      clearError();
      setProcessing(true);

      // Get image dimensions
      const dimensions = await getImageDimensions(file);
      
      // Create preview URL
      const previewUrl = await createImagePreview(file);
      
      // Resize image if needed for processing
      let processedFile = file;
      if (dimensions.width > 1920 || dimensions.height > 1080) {
        const resizedBlob = await resizeImage(file, 1920, 1080, 0.9);
        processedFile = new File([resizedBlob], file.name, { type: file.type });
      }

      // Create uploaded image object
      const uploadedImage: UploadedImage = {
        id: generateId(),
        file: processedFile,
        url: previewUrl,
        width: dimensions.width,
        height: dimensions.height,
        uploadedAt: new Date(),
        processed: false,
        detectedProducts: [],
      };

      // Set as current image
      setCurrentImage(uploadedImage);

      // Simulate processing delay (replace with actual VLM processing)
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';
      setError(errorMessage);
      throw error;
    } finally {
      setProcessing(false);
    }
  }, [setCurrentImage, setProcessing, setError, clearError]);

  return {
    uploadImage,
  };
};

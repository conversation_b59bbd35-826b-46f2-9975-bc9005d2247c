import { useCallback, useMemo } from 'react';
import { useAppStore } from '../stores/appStore';
import type { DetectedProduct, ProductInfo, FilterOptions } from '../types';

export const useFilters = () => {
  const { 
    filters, 
    setFilters, 
    currentImage, 
    productInfo,
    getFilteredProducts 
  } = useAppStore();

  // Update specific filter sections
  const updateDietaryFilters = useCallback((dietary: FilterOptions['dietary']) => {
    setFilters({ ...filters, dietary });
  }, [filters, setFilters]);

  const updateExcludeFilters = useCallback((exclude: FilterOptions['exclude']) => {
    setFilters({ ...filters, exclude });
  }, [filters, setFilters]);

  const updateCategoryFilters = useCallback((categories: string[]) => {
    setFilters({ ...filters, categories });
  }, [filters, setFilters]);

  // Toggle individual filters
  const toggleDietaryFilter = useCallback((key: keyof FilterOptions['dietary']) => {
    setFilters({
      ...filters,
      dietary: {
        ...filters.dietary,
        [key]: !filters.dietary[key],
      },
    });
  }, [filters, setFilters]);

  const toggleExcludeFilter = useCallback((key: keyof Omit<FilterOptions['exclude'], 'allergens'>) => {
    setFilters({
      ...filters,
      exclude: {
        ...filters.exclude,
        [key]: !filters.exclude[key],
      },
    });
  }, [filters, setFilters]);

  // Allergen management
  const addAllergenExclusion = useCallback((allergen: string) => {
    if (!filters.exclude.allergens.includes(allergen)) {
      setFilters({
        ...filters,
        exclude: {
          ...filters.exclude,
          allergens: [...filters.exclude.allergens, allergen],
        },
      });
    }
  }, [filters, setFilters]);

  const removeAllergenExclusion = useCallback((allergen: string) => {
    setFilters({
      ...filters,
      exclude: {
        ...filters.exclude,
        allergens: filters.exclude.allergens.filter(a => a !== allergen),
      },
    });
  }, [filters, setFilters]);

  // Category management
  const addCategoryFilter = useCallback((category: string) => {
    if (!filters.categories.includes(category)) {
      setFilters({
        ...filters,
        categories: [...filters.categories, category],
      });
    }
  }, [filters, setFilters]);

  const removeCategoryFilter = useCallback((category: string) => {
    setFilters({
      ...filters,
      categories: filters.categories.filter(c => c !== category),
    });
  }, [filters, setFilters]);

  // Clear filters
  const clearAllFilters = useCallback(() => {
    setFilters({
      dietary: {
        vegan: false,
        vegetarian: false,
        glutenFree: false,
        halal: false,
        kosher: false,
      },
      exclude: {
        pork: false,
        beef: false,
        alcohol: false,
        nitrates: false,
        allergens: [],
      },
      categories: [],
    });
  }, [setFilters]);

  const clearDietaryFilters = useCallback(() => {
    setFilters({
      ...filters,
      dietary: {
        vegan: false,
        vegetarian: false,
        glutenFree: false,
        halal: false,
        kosher: false,
      },
    });
  }, [filters, setFilters]);

  const clearExcludeFilters = useCallback(() => {
    setFilters({
      ...filters,
      exclude: {
        pork: false,
        beef: false,
        alcohol: false,
        nitrates: false,
        allergens: [],
      },
    });
  }, [filters, setFilters]);

  // Filter statistics
  const filterStats = useMemo(() => {
    const totalProducts = currentImage?.detectedProducts?.length || 0;
    const filteredProducts = getFilteredProducts();
    const filteredCount = filteredProducts.length;
    
    const activeFiltersCount = 
      Object.values(filters.dietary).filter(Boolean).length +
      Object.values(filters.exclude).filter((value, index) => 
        index < 4 ? Boolean(value) : false
      ).length +
      filters.exclude.allergens.length +
      filters.categories.length;

    return {
      totalProducts,
      filteredCount,
      hiddenCount: totalProducts - filteredCount,
      activeFiltersCount,
      hasActiveFilters: activeFiltersCount > 0,
      filterEfficiency: totalProducts > 0 ? (filteredCount / totalProducts) * 100 : 100,
    };
  }, [currentImage, getFilteredProducts, filters]);

  // Get available categories from current products
  const availableCategories = useMemo(() => {
    if (!currentImage?.detectedProducts) return [];
    
    const categories = new Set<string>();
    currentImage.detectedProducts.forEach(product => {
      const info = productInfo[product.id];
      if (info?.category) {
        categories.add(info.category);
      }
    });
    
    return Array.from(categories).sort();
  }, [currentImage, productInfo]);

  // Get available allergens from current products
  const availableAllergens = useMemo(() => {
    if (!currentImage?.detectedProducts) return [];
    
    const allergens = new Set<string>();
    currentImage.detectedProducts.forEach(product => {
      const info = productInfo[product.id];
      if (info?.allergens) {
        info.allergens.forEach(allergen => allergens.add(allergen));
      }
    });
    
    return Array.from(allergens).sort();
  }, [currentImage, productInfo]);

  // Check if a product passes all filters
  const doesProductPassFilters = useCallback((
    product: DetectedProduct, 
    info?: ProductInfo
  ): boolean => {
    if (!info) return true; // Show products without info by default

    // Check dietary filters
    if (filters.dietary.vegan && !info.dietaryTags?.includes('vegan')) {
      return false;
    }
    if (filters.dietary.vegetarian && !info.dietaryTags?.includes('vegetarian')) {
      return false;
    }
    if (filters.dietary.glutenFree && !info.dietaryTags?.includes('gluten-free')) {
      return false;
    }
    if (filters.dietary.halal && !info.dietaryTags?.includes('halal')) {
      return false;
    }
    if (filters.dietary.kosher && !info.dietaryTags?.includes('kosher')) {
      return false;
    }

    // Check exclusion filters
    if (filters.exclude.pork && info.dietaryTags?.includes('contains-pork')) {
      return false;
    }
    if (filters.exclude.beef && info.dietaryTags?.includes('contains-beef')) {
      return false;
    }
    if (filters.exclude.alcohol && info.dietaryTags?.includes('contains-alcohol')) {
      return false;
    }
    if (filters.exclude.nitrates && info.dietaryTags?.includes('contains-nitrates')) {
      return false;
    }

    // Check allergen exclusions
    if (filters.exclude.allergens.length > 0 && info.allergens) {
      const hasExcludedAllergen = filters.exclude.allergens.some(allergen =>
        info.allergens?.some(productAllergen => 
          productAllergen.toLowerCase().includes(allergen.toLowerCase())
        )
      );
      if (hasExcludedAllergen) return false;
    }

    // Check category filters
    if (filters.categories.length > 0 && info.category) {
      return filters.categories.includes(info.category);
    }

    return true;
  }, [filters]);

  // Save/load filter presets
  const saveFilterPreset = useCallback((name: string) => {
    const presets = JSON.parse(localStorage.getItem('neovizion-filter-presets') || '{}');
    presets[name] = filters;
    localStorage.setItem('neovizion-filter-presets', JSON.stringify(presets));
  }, [filters]);

  const loadFilterPreset = useCallback((name: string) => {
    const presets = JSON.parse(localStorage.getItem('neovizion-filter-presets') || '{}');
    if (presets[name]) {
      setFilters(presets[name]);
    }
  }, [setFilters]);

  const getFilterPresets = useCallback(() => {
    return JSON.parse(localStorage.getItem('neovizion-filter-presets') || '{}');
  }, []);

  const deleteFilterPreset = useCallback((name: string) => {
    const presets = JSON.parse(localStorage.getItem('neovizion-filter-presets') || '{}');
    delete presets[name];
    localStorage.setItem('neovizion-filter-presets', JSON.stringify(presets));
  }, []);

  return {
    // Current filters
    filters,
    
    // Filter actions
    setFilters,
    updateDietaryFilters,
    updateExcludeFilters,
    updateCategoryFilters,
    toggleDietaryFilter,
    toggleExcludeFilter,
    addAllergenExclusion,
    removeAllergenExclusion,
    addCategoryFilter,
    removeCategoryFilter,
    
    // Clear actions
    clearAllFilters,
    clearDietaryFilters,
    clearExcludeFilters,
    
    // Statistics
    filterStats,
    
    // Available options
    availableCategories,
    availableAllergens,
    
    // Utilities
    doesProductPassFilters,
    getFilteredProducts,
    
    // Presets
    saveFilterPreset,
    loadFilterPreset,
    getFilterPresets,
    deleteFilterPreset,
  };
};

import { useCallback, useState } from 'react';
import { useAppStore } from '../stores/appStore';
import { openFoodFactsApi } from '../services/openFoodFactsApi';
import { debounce } from '../utils';
import type { DetectedProduct, ProductInfo } from '../types';

export const useProductInfo = () => {
  const { setProductInfo, getProductInfo, setError } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);

  // Fetch product info by barcode
  const fetchProductByBarcode = useCallback(async (barcode: string): Promise<ProductInfo | null> => {
    try {
      setIsLoading(true);
      const productInfo = await openFoodFactsApi.getProductByBarcode(barcode);
      
      if (productInfo) {
        setProductInfo(productInfo.id, productInfo);
      }
      
      return productInfo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product info';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [setProductInfo, setError]);

  // Search products by name and brand
  const searchProducts = useCallback(async (
    query: string, 
    limit: number = 20
  ): Promise<ProductInfo[]> => {
    try {
      setIsLoading(true);
      const products = await openFoodFactsApi.searchProducts(query, limit);
      
      // Cache all found products
      products.forEach(product => {
        setProductInfo(product.id, product);
      });
      
      return products;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search products';
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [setProductInfo, setError]);

  // Search by brand and name combination
  const searchByBrandAndName = useCallback(async (
    brand: string, 
    name: string
  ): Promise<ProductInfo[]> => {
    try {
      setIsLoading(true);
      const products = await openFoodFactsApi.searchByBrandAndName(brand, name);
      
      // Cache all found products
      products.forEach(product => {
        setProductInfo(product.id, product);
      });
      
      return products;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search products';
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [setProductInfo, setError]);

  // Fetch product info for detected product
  const fetchProductInfo = useCallback(async (detectedProduct: DetectedProduct): Promise<ProductInfo | null> => {
    // Check if we already have info for this product
    const existingInfo = getProductInfo(detectedProduct.id);
    if (existingInfo) {
      return existingInfo;
    }

    try {
      setIsLoading(true);
      let productInfo: ProductInfo | null = null;

      // Try barcode first if available
      if (detectedProduct.barcode) {
        productInfo = await openFoodFactsApi.getProductByBarcode(detectedProduct.barcode);
      }

      // If no barcode or barcode search failed, try name/brand search
      if (!productInfo && (detectedProduct.name || detectedProduct.brand)) {
        const searchQuery = [detectedProduct.brand, detectedProduct.name]
          .filter(Boolean)
          .join(' ');
        
        if (searchQuery.trim()) {
          const searchResults = await openFoodFactsApi.searchProducts(searchQuery, 5);
          
          // Take the first result that seems to match
          productInfo = searchResults.find(product => {
            const nameMatch = detectedProduct.name && 
              product.name.toLowerCase().includes(detectedProduct.name.toLowerCase());
            const brandMatch = detectedProduct.brand && 
              product.brand?.toLowerCase().includes(detectedProduct.brand.toLowerCase());
            
            return nameMatch || brandMatch;
          }) || searchResults[0] || null;
        }
      }

      if (productInfo) {
        // Update the product info with detected product ID for consistency
        const updatedInfo: ProductInfo = {
          ...productInfo,
          id: detectedProduct.id, // Use detected product ID as key
        };
        
        setProductInfo(detectedProduct.id, updatedInfo);
        return updatedInfo;
      }

      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch product info';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [getProductInfo, setProductInfo, setError]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string, callback: (results: ProductInfo[]) => void) => {
      const results = await searchProducts(query);
      callback(results);
    }, 500),
    [searchProducts]
  );

  // Batch fetch product info for multiple detected products
  const fetchMultipleProductInfo = useCallback(async (
    detectedProducts: DetectedProduct[]
  ): Promise<Record<string, ProductInfo | null>> => {
    const results: Record<string, ProductInfo | null> = {};
    
    // Process products in batches to respect rate limits
    const batchSize = 5;
    const batches = [];
    
    for (let i = 0; i < detectedProducts.length; i += batchSize) {
      batches.push(detectedProducts.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const batchPromises = batch.map(async (product) => {
        const info = await fetchProductInfo(product);
        results[product.id] = info;
        return info;
      });

      await Promise.all(batchPromises);
      
      // Small delay between batches to be respectful to the API
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }, [fetchProductInfo]);

  // Get API cache statistics
  const getCacheStats = useCallback(() => {
    return openFoodFactsApi.getCacheStats();
  }, []);

  // Clear API cache
  const clearCache = useCallback(() => {
    openFoodFactsApi.clearCache();
  }, []);

  return {
    // State
    isLoading,
    
    // Actions
    fetchProductByBarcode,
    searchProducts,
    searchByBrandAndName,
    fetchProductInfo,
    fetchMultipleProductInfo,
    debouncedSearch,
    
    // Utilities
    getCacheStats,
    clearCache,
  };
};

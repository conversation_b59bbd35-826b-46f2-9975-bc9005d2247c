import { useCallback, useState } from 'react';
import { useAppStore } from '../stores/appStore';
import { vlmService, VLMServiceFactory } from '../services/vlmService';
import type { VLMServiceConfig, DetectedProduct } from '../types';

export const useVLM = () => {
  const { 
    updateImageProducts, 
    setError, 
    setProcessing,
    currentImage 
  } = useAppStore();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [currentService, setCurrentService] = useState(vlmService);

  // Process image with VLM
  const processImage = useCallback(async (
    imageFile: File,
    imageId: string,
    config?: VLMServiceConfig
  ): Promise<DetectedProduct[]> => {
    try {
      setIsProcessing(true);
      setProcessingProgress(0);
      setProcessing(true);
      setError(null);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Process with VLM service
      const response = await currentService.detectProducts(imageFile, config);
      
      // Complete progress
      clearInterval(progressInterval);
      setProcessingProgress(100);

      // Update store with detected products
      updateImageProducts(imageId, response.detectedProducts);

      // Clean up progress after delay
      setTimeout(() => {
        setProcessingProgress(0);
      }, 1000);

      return response.detectedProducts;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process image';
      setError(errorMessage);
      setProcessingProgress(0);
      throw error;
    } finally {
      setIsProcessing(false);
      setProcessing(false);
    }
  }, [currentService, updateImageProducts, setError, setProcessing]);

  // Process current image
  const processCurrentImage = useCallback(async (config?: VLMServiceConfig) => {
    if (!currentImage) {
      throw new Error('No image to process');
    }

    return processImage(currentImage.file, currentImage.id, config);
  }, [currentImage, processImage]);

  // Switch VLM service
  const switchService = useCallback((
    type: 'mock' | 'openai',
    config?: VLMServiceConfig
  ) => {
    const newService = VLMServiceFactory.createService(type, config);
    setCurrentService(newService);
  }, []);

  // Get service info
  const getServiceInfo = useCallback(() => {
    return currentService.getModelInfo();
  }, [currentService]);

  // Check if service is available
  const isServiceAvailable = useCallback(() => {
    return currentService.isAvailable();
  }, [currentService]);

  // Get available services
  const getAvailableServices = useCallback(() => {
    return VLMServiceFactory.getAvailableServices();
  }, []);

  // Reprocess with different settings
  const reprocessImage = useCallback(async (
    config?: VLMServiceConfig
  ): Promise<DetectedProduct[]> => {
    if (!currentImage) {
      throw new Error('No image to reprocess');
    }

    return processImage(currentImage.file, currentImage.id, config);
  }, [currentImage, processImage]);

  // Validate VLM configuration
  const validateConfig = useCallback((config: VLMServiceConfig): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (config.confidence_threshold !== undefined) {
      if (config.confidence_threshold < 0 || config.confidence_threshold > 1) {
        errors.push('Confidence threshold must be between 0 and 1');
      }
    }

    if (config.max_detections !== undefined) {
      if (config.max_detections < 1 || config.max_detections > 100) {
        errors.push('Max detections must be between 1 and 100');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }, []);

  // Get processing statistics
  const getProcessingStats = useCallback(() => {
    if (!currentImage?.detectedProducts) {
      return null;
    }

    const products = currentImage.detectedProducts;
    const confidences = products.map(p => p.confidence);
    
    return {
      totalProducts: products.length,
      averageConfidence: confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length,
      minConfidence: Math.min(...confidences),
      maxConfidence: Math.max(...confidences),
      highConfidenceCount: confidences.filter(conf => conf > 0.8).length,
      mediumConfidenceCount: confidences.filter(conf => conf > 0.6 && conf <= 0.8).length,
      lowConfidenceCount: confidences.filter(conf => conf <= 0.6).length,
    };
  }, [currentImage]);

  // Export detected products
  const exportDetections = useCallback((format: 'json' | 'csv' = 'json') => {
    if (!currentImage?.detectedProducts) {
      throw new Error('No detections to export');
    }

    const products = currentImage.detectedProducts;
    
    if (format === 'json') {
      return JSON.stringify(products, null, 2);
    } else if (format === 'csv') {
      const headers = ['id', 'name', 'brand', 'confidence', 'category', 'barcode', 'x', 'y', 'width', 'height'];
      const rows = products.map(product => [
        product.id,
        product.name || '',
        product.brand || '',
        product.confidence.toFixed(3),
        product.category || '',
        product.barcode || '',
        product.boundingBox.x.toFixed(2),
        product.boundingBox.y.toFixed(2),
        product.boundingBox.width.toFixed(2),
        product.boundingBox.height.toFixed(2),
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    throw new Error(`Unsupported export format: ${format}`);
  }, [currentImage]);

  // Download detections file
  const downloadDetections = useCallback((format: 'json' | 'csv' = 'json') => {
    try {
      const data = exportDetections(format);
      const blob = new Blob([data], { 
        type: format === 'json' ? 'application/json' : 'text/csv' 
      });
      
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `detections_${Date.now()}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to download detections';
      setError(errorMessage);
    }
  }, [exportDetections, setError]);

  return {
    // Processing state
    isProcessing,
    processingProgress,
    
    // Actions
    processImage,
    processCurrentImage,
    reprocessImage,
    
    // Service management
    switchService,
    getServiceInfo,
    isServiceAvailable,
    getAvailableServices,
    
    // Configuration
    validateConfig,
    
    // Statistics and export
    getProcessingStats,
    exportDetections,
    downloadDetections,
  };
};

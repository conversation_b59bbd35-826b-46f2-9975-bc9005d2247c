// Core application types for NeoVizion

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
  confidence?: number;
}

export interface DetectedProduct {
  id: string;
  name: string;
  brand?: string;
  boundingBox: BoundingBox;
  confidence: number;
  category?: string;
  barcode?: string;
  extractedText?: string[];
}

export interface ProductInfo {
  id: string;
  name: string;
  brand?: string;
  barcode?: string;
  ingredients?: string[];
  nutritionFacts?: NutritionFacts;
  allergens?: string[];
  dietaryTags?: DietaryTag[];
  imageUrl?: string;
  category?: string;
  description?: string;
  packaging?: string;
  servingSize?: string;
}

export interface NutritionFacts {
  energy?: number; // kcal per 100g
  fat?: number; // g per 100g
  saturatedFat?: number;
  carbohydrates?: number;
  sugars?: number;
  fiber?: number;
  proteins?: number;
  salt?: number;
  sodium?: number;
}

export type DietaryTag = 
  | 'vegan'
  | 'vegetarian'
  | 'gluten-free'
  | 'halal'
  | 'kosher'
  | 'organic'
  | 'non-gmo'
  | 'contains-pork'
  | 'contains-beef'
  | 'contains-alcohol'
  | 'contains-nitrates';

export interface UploadedImage {
  id: string;
  file: File;
  url: string;
  width: number;
  height: number;
  uploadedAt: Date;
  processed: boolean;
  detectedProducts?: DetectedProduct[];
}

export interface FilterOptions {
  dietary: {
    vegan: boolean;
    vegetarian: boolean;
    glutenFree: boolean;
    halal: boolean;
    kosher: boolean;
  };
  exclude: {
    pork: boolean;
    beef: boolean;
    alcohol: boolean;
    nitrates: boolean;
    allergens: string[];
  };
  categories: string[];
}

export interface AppState {
  currentImage: UploadedImage | null;
  selectedProduct: DetectedProduct | null;
  productInfo: Record<string, ProductInfo>;
  filters: FilterOptions;
  isProcessing: boolean;
  error: string | null;
}

// API Response types for OpenFoodFacts
export interface OpenFoodFactsProduct {
  code: string;
  product: {
    product_name?: string;
    brands?: string;
    ingredients_text?: string;
    nutriments?: {
      energy_100g?: number;
      fat_100g?: number;
      'saturated-fat_100g'?: number;
      carbohydrates_100g?: number;
      sugars_100g?: number;
      fiber_100g?: number;
      proteins_100g?: number;
      salt_100g?: number;
      sodium_100g?: number;
    };
    allergens?: string;
    labels?: string;
    categories?: string;
    image_url?: string;
    packaging?: string;
    serving_size?: string;
  };
  status: number;
  status_verbose: string;
}

export interface VLMResponse {
  detectedProducts: DetectedProduct[];
  processingTime: number;
  confidence: number;
}

// UI Component Props
export interface ImageViewerProps {
  image: UploadedImage;
  onProductSelect: (product: DetectedProduct) => void;
  selectedProduct?: DetectedProduct;
  filteredProducts?: DetectedProduct[];
}

export interface ProductPanelProps {
  product: DetectedProduct;
  productInfo?: ProductInfo;
  onClose: () => void;
}

export interface FilterPanelProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  productCount: number;
  filteredCount: number;
}

export interface UploadAreaProps {
  onImageUpload: (file: File) => void;
  isProcessing: boolean;
}

// Error types
export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class APIError extends AppError {
  constructor(message: string, public statusCode: number, details?: any) {
    super(message, 'API_ERROR', details);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', details);
  }
}

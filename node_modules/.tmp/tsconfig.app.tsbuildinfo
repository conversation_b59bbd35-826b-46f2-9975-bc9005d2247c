{"root": ["../../src/App.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/Dashboard.tsx", "../../src/components/Layout.tsx", "../../src/components/features/BoundingBox.tsx", "../../src/components/features/FilterPanel.tsx", "../../src/components/features/ImageViewer.tsx", "../../src/components/features/ProductCard.tsx", "../../src/components/features/ProductPanel.tsx", "../../src/components/features/SearchBar.tsx", "../../src/components/features/UploadArea.tsx", "../../src/components/features/VLMConfig.tsx", "../../src/components/features/index.ts", "../../src/components/ui/Button.tsx", "../../src/components/ui/Card.tsx", "../../src/components/ui/ErrorBoundary.tsx", "../../src/components/ui/Loading.tsx", "../../src/components/ui/Modal.tsx", "../../src/components/ui/index.ts", "../../src/hooks/index.ts", "../../src/hooks/useFilters.ts", "../../src/hooks/useImageUpload.ts", "../../src/hooks/useImageViewer.ts", "../../src/hooks/useProductInfo.ts", "../../src/hooks/useVLM.ts", "../../src/services/index.ts", "../../src/services/openFoodFactsApi.ts", "../../src/services/vlmService.ts", "../../src/stores/appStore.ts", "../../src/types/index.ts", "../../src/utils/imageProcessing.ts", "../../src/utils/index.ts", "../../src/utils/performance.ts"], "errors": true, "version": "5.8.3"}